
/* Ensures Tailwind base styles are applied if not using @tailwind directives */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

body {
    margin: 0;
    font-family: '<PERSON><PERSON> Petch', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #111827; /* bg-gray-900 */
    color: #f3f4f6; /* text-gray-100 */
}

/* Custom Alert Styles */
.custom-alert {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(-40px); /* Initial position for animation */
  color: white;
  padding: 12px 24px;
  border-radius: 6px; /* consistent with rounded-md */
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 250px;
  text-align: center;
}

.custom-alert.success {
  background-color: #0891b2; /* Tailwind cyan-600 (Primary color from design system) */
}

.custom-alert.error {
  background-color: #dc2626; /* Tailwind red-600 (Destructive color from design system) */
}

.custom-alert.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0); /* Final position */
}

/* Prose styles customization if needed, assuming @tailwindcss/typography is not fully available via CDN */
.prose {
    max-width: none; /* Remove max-width constraint if container handles it */
}

.prose strong {
    color: inherit; /* Ensure strong tags inherit color correctly in dark mode */
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: inherit;
}

.prose-invert code::before, .prose-invert code::after {
    content: none; /* Removes backticks from inline code if not desired */
}

/* Add a slightly darker background for the generated plan text area for better contrast */
.bg-gray-750 {
    background-color: #303742; /* A custom shade between gray-700 and gray-800 */
}
