
import React, { useState, useEffect } from 'react';
import <PERSON>actDOM from 'react-dom/client';
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";

interface PlanSection {
  title: string;
  content: string;
  originalNumber?: string;
}

interface UserTypeOption {
  name: string;
  icon: string; // Material Icon name
}

// Main App component
const App = () => {
  const [intelligenceNeeds, setIntelligenceNeeds] = useState('');
  const [collectionPlan, setCollectionPlan] = useState(''); // Stores raw AI response
  const [parsedPlan, setParsedPlan] = useState<PlanSection[]>([]); // Stores structured plan
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [userType, setUserType] = useState('Intelligence Analyst');
  const [investigationType, setInvestigationType] = useState('Subject of Interest');
  const [alertMessage, setAlertMessage] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  // Initialize the GoogleGenAI client
  const apiKey = process.env.API_KEY;
  let ai: GoogleGenAI | null = null;
  if (apiKey) {
    ai = new GoogleGenAI({ apiKey });
  } else {
    console.error("API_KEY environment variable is not set.");
  }

  const userTypeOptions: UserTypeOption[] = [
    { name: 'Intelligence Analyst', icon: 'insights' },
    { name: 'Law Enforcement Officer', icon: 'local_police' },
    { name: 'Fraud Investigator', icon: 'manage_search' },
    { name: 'Corporate Security Analyst', icon: 'security' },
    { name: 'Journalist', icon: 'article' },
    { name: 'Researcher', icon: 'biotech' }
  ];

  const investigationTypeOptions = [
    'Subject of Interest', 'Company Due Diligence', 'Person of Interest',
    'Threat Assessment', 'Event Security Planning', 'Competitor Analysis'
  ];

  const showAlert = (message: string, type: 'success' | 'error') => {
    setAlertMessage({ message, type });
    setTimeout(() => {
      setAlertMessage(null);
    }, 3000);
  };

  const parseAndSetPlan = (rawText: string) => {
    setCollectionPlan(rawText); // Store raw text for copying

    const sections: PlanSection[] = [];
    const sectionRegex = /(\d+)\.\s*\*\*(.*?)\*\*(?::)?\s*([\s\S]*?)(?=\n\s*\d+\.\s*\*\*|$)/g;
    let match;
    while ((match = sectionRegex.exec(rawText)) !== null) {
      const originalNumber = match[1] + ".";
      const title = match[2].trim();
      let content = match[3].trim();
      content = content.replace(/\*\*/g, '');
      sections.push({ originalNumber, title, content });
    }

    if (sections.length > 0) {
      setParsedPlan(sections);
    } else if (rawText && rawText.trim().length > 0) {
      const cleanedFallbackContent = rawText.replace(/\*\*/g, '');
      setParsedPlan([{ title: "Generated Intelligence Collection Plan", content: cleanedFallbackContent }]);
    } else {
      setParsedPlan([]); 
    }
  };

  const generateCollectionPlan = async () => {
    if (!ai) {
      setError("AI Service not initialized. Check API Key.");
      setIsLoading(false);
      return;
    }
    if (intelligenceNeeds.trim() === '') {
        setError("Please enter your intelligence needs.");
        return;
    }

    setCollectionPlan('');
    setParsedPlan([]);
    setError('');
    setIsLoading(true);

    const prompt = `As a ${userType} conducting a ${investigationType} investigation, I need to develop an Intelligence Collection Plan (ICP) based on the following intelligence needs: "${intelligenceNeeds}".

    Please provide a structured plan that includes:
    1.  **Specific Information Requirements (SIRs):** Break down the intelligence needs into precise, answerable questions relevant to a ${userType} and a ${investigationType} investigation.
    2.  **Indicators:** What observable phenomena or activities would indicate the presence or absence of the SIRs, considering the context of a ${investigationType} investigation?
    3.  **Collection Methods/Sources:** Suggest potential methods (e.g., HUMINT, OSINT, SIGINT, Financial Intelligence) and types of sources specific to a ${userType} and a ${investigationType} investigation to collect information for each SIR.
    4.  **Priority (High/Medium/Low):** Assign a priority level to each SIR.
    5.  **Reporting Requirements:** What format or level of detail is needed for the final intelligence report related to these SIRs, keeping in mind the audience of a ${userType}?
    6.  **Suggested Information Sources/Platforms:** Based on the SIRs and collection methods, suggest general types of online platforms, databases, or public websites (e.g., specific government registries, open-source intelligence databases, financial reporting sites, social media platforms, academic journals) that a ${userType} would typically consult for a ${investigationType} investigation.

    Ensure the plan is actionable and detailed enough for a collection manager.`;

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: 'gemini-2.5-flash-preview-04-17',
        contents: prompt,
      });

      const text = response.text;
      if (text) {
        parseAndSetPlan(text);
      } else {
        setError('No content generated or unexpected response structure.');
        console.error('API response:', response);
        setParsedPlan([]);
      }
    } catch (err: any) {
      console.error('Error generating collection plan:', err);
      setError(`Failed to generate collection plan: ${err.message || 'An unknown error occurred'}`);
      setParsedPlan([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    if (!collectionPlan) return; 
    navigator.clipboard.writeText(collectionPlan)
      .then(() => {
        showAlert('Raw collection plan copied to clipboard!', 'success');
      })
      .catch(err => {
        console.warn('Async clipboard copy failed, trying fallback: ', err);
        try {
          const textarea = document.createElement('textarea');
          textarea.value = collectionPlan;
          textarea.style.position = 'fixed'; 
          textarea.style.opacity = '0';
          document.body.appendChild(textarea);
          textarea.select();
          const success = document.execCommand('copy');
          document.body.removeChild(textarea);
          if (success) {
            showAlert('Raw collection plan copied to clipboard! (fallback)', 'success');
          } else {
            throw new Error('Fallback copy command failed');
          }
        } catch (fallbackErr) {
          console.error('Fallback copy failed: ', fallbackErr);
          showAlert('Failed to copy raw collection plan.', 'error');
        }
      });
  };
  
  useEffect(() => {
    if (!apiKey && !error) {
        setError("API Key is missing. Please ensure it's configured in the environment.");
    }
  }, [apiKey, error]);


  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 text-gray-100 p-4 sm:p-8 flex flex-col items-center font-chakra-petch">
      <div className="bg-gray-800 rounded-lg shadow-xl p-6 sm:p-8 w-full max-w-4xl border border-gray-700">
        <h1 className="text-3xl sm:text-4xl font-bold text-center mb-6 text-cyan-400">
          Intelligence Collection Planning Aid
        </h1>
        <p className="text-center text-gray-400 mb-8 px-2">
          Select your role and investigation type, then enter your raw intelligence needs below. I will help you draft a structured Intelligence Collection Plan (ICP).
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8 mb-8">
          <div>
            <label htmlFor="user-type-selector-label" className="block text-gray-300 text-lg font-medium mb-3">
              Your User Type:
            </label>
            <div id="user-type-selector" className="flex flex-wrap gap-2 md:gap-3" role="radiogroup" aria-labelledby="user-type-selector-label">
              {userTypeOptions.map((option) => (
                <button
                  key={option.name}
                  type="button"
                  role="radio"
                  aria-checked={userType === option.name}
                  onClick={() => setUserType(option.name)}
                  className={`flex flex-col items-center justify-center p-2 rounded-lg border text-xs transition-colors duration-200 focus:outline-none 
                    bg-gray-700 text-cyan-400 w-28 h-24
                    focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-gray-800
                    ${userType === option.name 
                      ? 'border-cyan-500 ring-2 ring-cyan-500 shadow-lg shadow-cyan-500/30' 
                      : 'border-gray-600 hover:bg-gray-600 hover:border-gray-500'
                    }`}
                  aria-label={option.name}
                >
                  <span className="material-icons text-3xl mb-1">{option.icon}</span>
                  <span className="text-center leading-tight">{option.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div>
            <label htmlFor="investigation-type" className="block text-gray-300 text-lg font-medium mb-3">
              Investigation Type:
            </label>
            <select
              id="investigation-type"
              aria-label="Select investigation type"
              className="w-full p-3 rounded-md bg-gray-700 text-white border border-gray-600 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-all duration-200"
              value={investigationType}
              onChange={(e) => setInvestigationType(e.target.value)}
            >
              {investigationTypeOptions.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mb-8">
          <label htmlFor="intelligence-needs" className="block text-gray-300 text-lg font-medium mb-2">
            Your Intelligence Needs / Questions:
          </label>
          <textarea
            id="intelligence-needs"
            aria-label="Enter your intelligence needs or questions"
            className="w-full p-4 rounded-md bg-gray-700 text-white border border-gray-600 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-all duration-200 resize-y min-h-[150px] sm:min-h-[120px]"
            placeholder="e.g., What are the capabilities of insurgent group X in region Y? What is their current leadership structure? What are their funding sources?"
            value={intelligenceNeeds}
            onChange={(e) => setIntelligenceNeeds(e.target.value)}
            rows={6}
          ></textarea>
        </div>

        <div className="flex justify-center mb-8">
          <button
            onClick={generateCollectionPlan}
            className="bg-cyan-600 hover:bg-cyan-500 active:bg-cyan-700 text-white font-semibold py-2 px-6 rounded-md shadow-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-400 focus:ring-offset-gray-800 disabled:bg-cyan-800 disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center text-base sm:text-lg"
            disabled={isLoading || intelligenceNeeds.trim() === '' || !ai}
            aria-label="Generate Intelligence Collection Plan"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
              </>
            ) : (
              'Generate Collection Plan'
            )}
          </button>
        </div>

        {error && (
          <div className="bg-red-600 text-white p-4 rounded-md mb-8 border border-red-500 text-center shadow-md" role="alert">
            <p className="font-semibold mb-1">Error:</p>
            <p>{error}</p>
          </div>
        )}

        {parsedPlan.length > 0 && (
          <div className="bg-gray-700 p-4 sm:p-6 rounded-lg border border-gray-600 shadow-xl">
            <h2 className="text-xl sm:text-2xl font-bold text-cyan-300 mb-6">Generated Intelligence Collection Plan:</h2>
            {parsedPlan.map((section, index) => (
              <div key={index} className="mb-6 last:mb-0">
                <h3 className="text-lg sm:text-xl font-semibold text-cyan-200 mb-2">
                  {section.originalNumber && <span className="mr-2 text-cyan-300">{section.originalNumber}</span>}
                  {section.title}
                </h3>
                <div
                  className="prose prose-sm sm:prose-base prose-invert text-gray-300 leading-relaxed pl-3 border-l-2 border-gray-500"
                  dangerouslySetInnerHTML={{ __html: section.content.replace(/\n\n/g, '<br /><br />').replace(/\n/g, '<br />') }}
                  aria-live="polite"
                />
              </div>
            ))}
             <button
              onClick={handleCopyToClipboard}
              className="mt-8 border border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white active:bg-cyan-600 font-semibold py-2 px-6 rounded-md shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-400 focus:ring-offset-gray-700"
              aria-label="Copy raw generated plan to clipboard"
            >
              Copy Raw Plan to Clipboard
            </button>
          </div>
        )}
      </div>
      {alertMessage && (
        <div
          className={`custom-alert ${alertMessage.type === 'success' ? 'success' : 'error'} ${alertMessage ? 'show' : ''}`}
          role="alert"
          aria-live="assertive"
        >
          {alertMessage.message}
        </div>
      )}
    </div>
  );
};

const container = document.getElementById('root');
if (container) {
  const root = ReactDOM.createRoot(container);
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
} else {
  console.error('Failed to find the root element');
}
