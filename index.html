
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intelligence Collection Planning Aid</title>
    <!-- Tailwind CSS JIT CDN Script -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom Tailwind Config (must be after the CDN script) -->
    <script>
        // Custom Tailwind CSS configuration to add font-chakra-petch
        tailwind.config = {
          theme: {
            extend: {
              fontFamily: {
                'chakra-petch': ['Chakra Petch', 'sans-serif'],
              },
            }
          }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Chakra+Petch:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="index.css">
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
    "@google/genai": "https://esm.sh/@google/genai",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-gray-900">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="index.tsx"></script>
<script type="module" src="/index.tsx"></script>
</body>
</html>
